import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy import stats
import matplotlib.font_manager as fm

# 設定中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# --------- config ----------
fs_a = 10000  # 加速度取樣率
fs_s = 10000  # 應變取樣率
win_ms = 50   # STFT/特徵窗長
hop_ms = 10
hp_cut = 1.0  # 高通去漂移
scratch_band = (2000, 8000)  # 刮擦高頻候選帶
crest_thr = 6.0              # 峰因子門檻
kurt_thr = 5.0               # 峭度門檻
wobble_pk_min_hz = 2.0

# 短時滑窗
win = int(fs_a*win_ms/1000)

def generate_xy_trajectory():
    """生成連續的XY座標軌跡數據"""

    # 總時間長度 (秒)
    total_time = 10.0
    t = np.linspace(0, total_time, int(fs_a * total_time))

    # 初始化XY座標
    x = np.zeros(len(t))
    y = np.zeros(len(t))

    # 事件時間點和標籤
    events = [
        (1.0, 1.5, "手臂移動", (2.0, 1.5)),      # 時間, 持續時間, 標籤, (x_移動, y_移動)
        (2.5, 0.8, "小幅移動", (0.5, -0.3)),
        (4.0, 0.2, "chuck開", (0.0, 0.0)),       # 原地振動
        (5.5, 0.6, "小幅移動", (-0.4, 0.6)),
        (7.0, 2.0, "手臂上升至頂", (1.2, 2.5))
    ]

    # 當前位置
    current_x, current_y = 0.0, 0.0

    # 為每個事件生成軌跡
    for i, (start_time, duration, label, movement) in enumerate(events):
        start_idx = int(start_time * fs_a)
        end_idx = int((start_time + duration) * fs_a)

        if start_idx >= len(t):
            break

        end_idx = min(end_idx, len(t))
        segment_length = end_idx - start_idx

        if label == "chuck開":
            # Chuck開事件：原地高頻振動
            event_t = np.linspace(0, duration, segment_length)
            vibration_x = 0.1 * np.sin(2 * np.pi * 50 * event_t) * np.exp(-event_t * 5)
            vibration_y = 0.1 * np.cos(2 * np.pi * 50 * event_t) * np.exp(-event_t * 5)
            x[start_idx:end_idx] = current_x + vibration_x
            y[start_idx:end_idx] = current_y + vibration_y
        else:
            # 其他事件：平滑移動
            target_x = current_x + movement[0]
            target_y = current_y + movement[1]

            # 生成平滑的移動軌跡
            progress = np.linspace(0, 1, segment_length)
            # 使用S曲線讓移動更自然
            smooth_progress = 3 * progress**2 - 2 * progress**3

            x[start_idx:end_idx] = current_x + (target_x - current_x) * smooth_progress
            y[start_idx:end_idx] = current_y + (target_y - current_y) * smooth_progress

            # 添加一些隨機抖動
            noise_scale = 0.05 if "小幅" in label else 0.1
            x[start_idx:end_idx] += np.random.normal(0, noise_scale, segment_length)
            y[start_idx:end_idx] += np.random.normal(0, noise_scale, segment_length)

            # 更新當前位置
            current_x, current_y = target_x, target_y

    # 填充事件之間的間隔
    for i in range(len(events) - 1):
        end_time = events[i][0] + events[i][1]
        next_start_time = events[i + 1][0]

        end_idx = int(end_time * fs_a)
        next_start_idx = int(next_start_time * fs_a)

        if end_idx < next_start_idx and end_idx < len(t) and next_start_idx <= len(t):
            # 線性插值填充間隔
            x[end_idx:next_start_idx] = np.linspace(x[end_idx-1], x[next_start_idx],
                                                   next_start_idx - end_idx)
            y[end_idx:next_start_idx] = np.linspace(y[end_idx-1], y[next_start_idx],
                                                   next_start_idx - end_idx)

    return t, x, y, events

def plot_xy_trajectory():
    """繪製XY座標軌跡圖"""

    # 生成數據
    t, x, y, events = generate_xy_trajectory()

    # 創建圖形
    fig, ax = plt.subplots(figsize=(12, 10))

    # 設置黑色背景
    fig.patch.set_facecolor('black')
    ax.set_facecolor('black')

    # 繪製軌跡線 - 使用橙色類似圖片
    ax.plot(x, y, color='#FF6B35', linewidth=2, alpha=0.9, label='軌跡路徑')

    # 標記起點和終點
    ax.plot(x[0], y[0], 'go', markersize=10, label='起點')
    ax.plot(x[-1], y[-1], 'ro', markersize=10, label='終點')

    # 添加事件標記點
    for start_time, duration, label, movement in events:
        start_idx = int(start_time * fs_a)
        if start_idx < len(x):
            ax.plot(x[start_idx], y[start_idx], 'yo', markersize=8)

            # 添加標籤
            ax.annotate(label,
                       (x[start_idx], y[start_idx]),
                       xytext=(10, 10), textcoords='offset points',
                       color='white', fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', color='yellow', alpha=0.7))

    # 添加方向箭頭（每隔一定間隔）
    arrow_interval = len(x) // 20  # 顯示20個箭頭
    for i in range(0, len(x) - arrow_interval, arrow_interval):
        dx = x[i + arrow_interval] - x[i]
        dy = y[i + arrow_interval] - y[i]
        if dx != 0 or dy != 0:  # 避免零向量
            ax.arrow(x[i], y[i], dx * 0.3, dy * 0.3,
                    head_width=0.05, head_length=0.05,
                    fc='cyan', ec='cyan', alpha=0.6)

    # 設置軸標籤和標題
    ax.set_xlabel('X 座標 (mm)', color='white', fontsize=12)
    ax.set_ylabel('Y 座標 (mm)', color='white', fontsize=12)
    ax.set_title('機械學習監控 - XY軌跡圖', color='white', fontsize=14, pad=20)

    # 設置軸顏色
    ax.tick_params(colors='white')
    ax.spines['bottom'].set_color('white')
    ax.spines['top'].set_color('white')
    ax.spines['right'].set_color('white')
    ax.spines['left'].set_color('white')

    # 設置網格
    ax.grid(True, alpha=0.3, color='gray')

    # 設置等比例軸
    ax.set_aspect('equal', adjustable='box')

    # 添加圖例
    ax.legend(loc='upper right', facecolor='black', edgecolor='white',
             labelcolor='white', framealpha=0.8)

    # 調整佈局
    plt.tight_layout()

    return fig, ax, t, x, y

def analyze_trajectory(t, x, y):
    """分析XY軌跡特徵"""

    # 計算移動距離
    dx = np.diff(x)
    dy = np.diff(y)
    distances = np.sqrt(dx**2 + dy**2)

    # 計算速度
    dt = np.diff(t)
    velocities = distances / (dt + 1e-12)

    # 計算加速度
    accelerations = np.diff(velocities) / (dt[:-1] + 1e-12)

    # 計算軌跡特徵
    total_distance = np.sum(distances)
    max_velocity = np.max(velocities) if len(velocities) > 0 else 0
    avg_velocity = np.mean(velocities) if len(velocities) > 0 else 0
    max_acceleration = np.max(np.abs(accelerations)) if len(accelerations) > 0 else 0

    # 計算軌跡範圍
    x_range = np.max(x) - np.min(x)
    y_range = np.max(y) - np.min(y)

    features = {
        'total_distance': total_distance,
        'max_velocity': max_velocity,
        'avg_velocity': avg_velocity,
        'max_acceleration': max_acceleration,
        'x_range': x_range,
        'y_range': y_range,
        'start_point': (x[0], y[0]),
        'end_point': (x[-1], y[-1])
    }

    return features

def main():
    """主函數"""
    print("生成XY軌跡圖...")

    # 繪製軌跡
    fig, ax, t, x, y = plot_xy_trajectory()

    # 分析軌跡
    features = analyze_trajectory(t, x, y)

    # 顯示統計信息
    print(f"軌跡總長度: {len(t)} 個採樣點")
    print(f"採樣率: {fs_a} Hz")
    print(f"時間範圍: {t[0]:.2f} - {t[-1]:.2f} 秒")
    print(f"總移動距離: {features['total_distance']:.2f} mm")
    print(f"最大速度: {features['max_velocity']:.2f} mm/s")
    print(f"平均速度: {features['avg_velocity']:.2f} mm/s")
    print(f"最大加速度: {features['max_acceleration']:.2f} mm/s²")
    print(f"X軸範圍: {features['x_range']:.2f} mm")
    print(f"Y軸範圍: {features['y_range']:.2f} mm")
    print(f"起點: ({features['start_point'][0]:.2f}, {features['start_point'][1]:.2f}) mm")
    print(f"終點: ({features['end_point'][0]:.2f}, {features['end_point'][1]:.2f}) mm")

    # 顯示圖形
    plt.show()

    return fig, features

if __name__ == "__main__":
    main()
