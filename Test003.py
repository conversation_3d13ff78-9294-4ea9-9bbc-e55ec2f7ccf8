import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy import stats
import matplotlib.font_manager as fm

# 設定中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# --------- config ----------
fs_a = 10000  # 加速度取樣率
fs_s = 10000  # 應變取樣率
win_ms = 50   # STFT/特徵窗長
hop_ms = 10
hp_cut = 1.0  # 高通去漂移
scratch_band = (2000, 8000)  # 刮擦高頻候選帶
crest_thr = 6.0              # 峰因子門檻
kurt_thr = 5.0               # 峭度門檻
wobble_pk_min_hz = 2.0

# 短時滑窗
win = int(fs_a*win_ms/1000)

def generate_waveform_data():
    """生成模擬波形數據，類似圖片中的波形"""
    
    # 總時間長度 (秒)
    total_time = 10.0
    t = np.linspace(0, total_time, int(fs_a * total_time))
    
    # 基礎噪聲
    base_noise = np.random.normal(0, 0.02, len(t))
    
    # 創建波形
    waveform = base_noise.copy()
    
    # 事件時間點和標籤
    events = [
        (1.0, 1.5, "手臂移動", 0.8),      # 時間, 持續時間, 標籤, 幅度
        (2.5, 0.8, "小幅移動", 0.3),
        (4.0, 0.2, "chuck開", 1.2),       # 短暫高峰
        (5.5, 0.6, "小幅移動", 0.25),
        (7.0, 2.0, "手臂上升至頂", 0.9)
    ]
    
    # 為每個事件添加波形
    for start_time, duration, label, amplitude in events:
        start_idx = int(start_time * fs_a)
        end_idx = int((start_time + duration) * fs_a)
        
        if label == "chuck開":
            # Chuck開事件：短暫的高頻尖峰
            event_t = np.linspace(0, duration, end_idx - start_idx)
            spike = amplitude * np.exp(-event_t * 8) * np.sin(2 * np.pi * 50 * event_t)
            waveform[start_idx:end_idx] += spike
        elif "手臂" in label:
            # 手臂移動：較大幅度的振盪
            event_t = np.linspace(0, duration, end_idx - start_idx)
            envelope = amplitude * np.exp(-event_t * 2) * (1 + 0.3 * np.sin(2 * np.pi * 3 * event_t))
            oscillation = envelope * np.sin(2 * np.pi * 15 * event_t)
            # 添加一些隨機變化
            random_component = 0.2 * amplitude * np.random.normal(0, 1, len(event_t))
            waveform[start_idx:end_idx] += oscillation + random_component
        else:
            # 小幅移動：較小的振盪
            event_t = np.linspace(0, duration, end_idx - start_idx)
            envelope = amplitude * np.exp(-event_t * 3)
            oscillation = envelope * np.sin(2 * np.pi * 10 * event_t)
            waveform[start_idx:end_idx] += oscillation
    
    return t, waveform, events

def plot_waveform():
    """繪製時序圖"""
    
    # 生成數據
    t, waveform, events = generate_waveform_data()
    
    # 創建圖形
    fig, ax = plt.subplots(figsize=(14, 6))
    
    # 設置黑色背景
    fig.patch.set_facecolor('black')
    ax.set_facecolor('black')
    
    # 繪製波形 - 使用橙色類似圖片
    ax.plot(t, waveform, color='#FF6B35', linewidth=1.2, alpha=0.9)
    
    # 添加事件標記
    for start_time, duration, label, amplitude in events:
        # 在事件位置添加垂直線
        ax.axvline(x=start_time, color='red', linestyle='--', alpha=0.7, linewidth=1)
        
        # 添加標籤
        y_pos = max(waveform) * 0.8 if amplitude > 0.5 else max(waveform) * 0.6
        ax.text(start_time, y_pos, label, 
                color='white', fontsize=10, ha='center', va='bottom',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))
    
    # 設置軸標籤和標題
    ax.set_xlabel('時間 (秒)', color='white', fontsize=12)
    ax.set_ylabel('振幅', color='white', fontsize=12)
    ax.set_title('機械學習監控測量畫面', color='white', fontsize=14, pad=20)
    
    # 設置軸顏色
    ax.tick_params(colors='white')
    ax.spines['bottom'].set_color('white')
    ax.spines['top'].set_color('white')
    ax.spines['right'].set_color('white')
    ax.spines['left'].set_color('white')
    
    # 設置網格
    ax.grid(True, alpha=0.3, color='gray')
    
    # 調整佈局
    plt.tight_layout()
    
    return fig, ax, t, waveform

def analyze_waveform(t, waveform):
    """分析波形特徵"""
    
    # 計算短時特徵
    hop = int(fs_a * hop_ms / 1000)
    features = []
    
    for start in range(0, len(waveform) - win, hop):
        seg = waveform[start:start + win]
        
        # 計算特徵
        rms = np.sqrt(np.mean(seg**2)) + 1e-12
        crest = np.max(np.abs(seg)) / rms
        kurt = signal.kurtosis(seg, fisher=False, bias=False)
        peak = np.max(np.abs(seg))
        
        time_point = start / fs_a
        features.append({
            'time': time_point,
            'rms': rms,
            'crest': crest,
            'kurtosis': kurt,
            'peak': peak
        })
    
    return features

def main():
    """主函數"""
    print("生成時序波形圖...")
    
    # 繪製波形
    fig, ax, t, waveform = plot_waveform()
    
    # 分析波形
    features = analyze_waveform(t, waveform)
    
    # 顯示一些統計信息
    print(f"波形總長度: {len(t)} 個採樣點")
    print(f"採樣率: {fs_a} Hz")
    print(f"時間範圍: {t[0]:.2f} - {t[-1]:.2f} 秒")
    print(f"短時滑窗長度: {win} 個採樣點 ({win_ms} ms)")
    
    # 找出峰值事件
    peak_threshold = np.std(waveform) * 3
    peak_events = [f for f in features if f['peak'] > peak_threshold]
    print(f"檢測到 {len(peak_events)} 個峰值事件")
    
    # 顯示圖形
    plt.show()
    
    return fig, features

if __name__ == "__main__":
    main()
