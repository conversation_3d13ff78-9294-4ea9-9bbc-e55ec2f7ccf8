import numpy as np
from scipy import signal
from collections import deque

# --------- config ----------
fs_a = 10000  # 加速度取樣率
fs_s = 10000  # 應變取樣率
win_ms = 50   # STFT/特徵窗長
hop_ms = 10
hp_cut = 1.0  # 高通去漂移
scratch_band = (2000, 8000)  # 刮擦高頻候選帶
crest_thr = 6.0              # 峰因子門檻（示意值，需標定）
kurt_thr = 5.0               # 峭度門檻（示意值）
wobble_pk_min_hz = 2.0

def butter_bp(low, high, fs, order=4):
    return signal.butter(order, [low/(fs/2), high/(fs/2)], btype='band')

def butter_hp(cut, fs, order=2):
    return signal.butter(order, cut/(fs/2), btype='high')

# 1) 前處理（加速度）
bhp = butter_hp(hp_cut, fs_a)
accel_hp = signal.filtfilt(*bhp, axis=0, x=accel)  # (T,3)

# 2) 晃動主頻（Welch）
f, Pxx = signal.welch(accel_hp[:,0], fs=fs_a, nperseg=int(fs_a*win_ms/1000))
pk_idx = np.argmax(Pxx)
f_peak = f[pk_idx] if f[pk_idx] >= wobble_pk_min_hz else 0.0

# 3) 刮擦偵測（帶通 + 短時峰因子/峭度）
bbp = butter_bp(*scratch_band, fs=fs_a)
acc_bp = signal.filtfilt(*bbp, axis=0, x=accel_hp)  # (T,3)
acc_norm = np.linalg.norm(acc_bp, axis=1)

# 短時滑窗
win = int(fs_a*win_ms/1000)
hop = int(fs_a*hop_ms/1000)
events = []
for start in range(0, len(acc_norm)-win, hop):
    seg = acc_norm[start:start+win]
    rms = np.sqrt(np.mean(seg**2)) + 1e-12
    crest = np.max(np.abs(seg)) / rms
    kurt = signal.kurtosis(seg, fisher=False, bias=False)
    if crest > crest_thr or kurt > kurt_thr:
        t = start / fs_a
        events.append({"t": t, "crest": crest, "kurt": kurt})

# 4) 重心偏位（示意：四應變規 ε1..ε4 已校正為 microstrain）
# 實務請用你的橋接與放大/零點校正後的應變值
eps = strain  # shape: (T,4)
eps_sum12 = np.clip(eps[:,0]+eps[:,1], 1e-9, None)
eps_sum34 = np.clip(eps[:,2]+eps[:,3], 1e-9, None)

kx, ky = 1.0, 1.0   # 實機標定得到
dx = kx * (eps[:,0]-eps[:,1]) / eps_sum12
dy = ky * (eps[:,2]-eps[:,3]) / eps_sum34

# 即時告警（帶遲滯）
def hysteresis(flag, val, hi, lo):
    return (val > hi) or (flag and val > lo)

cg_alert = False
cg_hi, cg_lo = 0.50, 0.35  # mm：示意
for v in np.sqrt(dx**2 + dy**2):
    cg_alert = hysteresis(cg_alert, v, cg_hi, cg_lo)
