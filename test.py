from PySide2 import QtWidgets, QtCore
import pyqtgraph as pg
import numpy as np

class RealtimeView(QtWidgets.QWidget):
    def __init__(self, fs=10000, buf_sec=5):
        super().__init__()
        self.fs = fs
        self.buf = int(fs*buf_sec)
        self.x = np.arange(self.buf)/fs
        self.y = np.zeros(self.buf)

        self.plot = pg.PlotWidget(title="Accel | BP band")
        self.curve = self.plot.plot(self.x, self.y, pen=pg.mkPen(width=2))
        self.freq_label = QtWidgets.QLabel("f_peak: -- Hz")
        self.alert = QtWidgets.QLabel("CG: OK"); self.alert.setStyleSheet("background:#16a34a;color:white;padding:6px;")

        layout = QtWidgets.QVBoxLayout(self)
        layout.addWidget(self.plot)
        layout.addWidget(self.freq_label)
        layout.addWidget(self.alert)

        self.ptr = 0
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.tick)
        self.timer.start(30)  # ~33 FPS

    def update_chunk(self, acc_bp_chunk, f_peak, cg_alert):
        n = len(acc_bp_chunk)
        self.y = np.roll(self.y, -n)
        self.y[-n:] = acc_bp_chunk
        self.curve.setData(self.x, self.y)
        self.freq_label.setText(f"f_peak: {f_peak:.1f} Hz")
        if cg_alert:
            self.alert.setText("CG: ALERT"); self.alert.setStyleSheet("background:#dc2626;color:white;padding:6px;")
        else:
            self.alert.setText("CG: OK"); self.alert.setStyleSheet("background:#16a34a;color:white;padding:6px;")

    def tick(self):
        pass  # 在你的資料接收回呼裡呼叫 update_chunk(...)

if __name__ == "__main__":
    app = QtWidgets.QApplication([])
    w = RealtimeView()
    w.resize(800, 500); w.show()
    app.exec_()
